
import { Card, CardContent } from "@/components/ui/card";
import { Heart, CheckCircle } from "lucide-react";

const CSRSection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our Commitment
          </h2>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-8 text-center">
              <Heart className="w-16 h-16 text-red-500 mx-auto mb-6" />
              <h3 className="text-2xl font-semibold mb-4">Community Support</h3>
              <p className="text-gray-600 text-lg">
                🐾 We proudly support Midrand SPCA
              </p>
            </CardContent>
          </Card>
          
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-8 text-center">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-6" />
              <h3 className="text-2xl font-semibold mb-4">Certifications</h3>
              <div className="text-gray-600 text-lg space-y-2">
                <p>✅ ISO 9001:2015</p>
                <p>✅ SABA Certified</p>
                <p>✅ WRAS Approved</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default CSRSection;
