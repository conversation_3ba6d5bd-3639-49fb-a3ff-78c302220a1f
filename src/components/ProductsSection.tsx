
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

const ProductsSection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Premium Powder Coatings
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            We supply powder coatings for industrial, commercial, and decorative use—engineered to last and formulated to shine.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <Card className="hover:shadow-lg transition-shadow duration-300 max-w-sm bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <img className="rounded-t-lg" src="https://powderlak.co.za/img/istockphoto-2012671247-1024x1024.jpg" alt="Industrial Grade" />
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-semibold mb-4">Industrial Grade</h3>
              <p className="text-gray-600">
                Heavy-duty coatings designed for extreme conditions and high-performance applications.
              </p>
            </CardContent>
          </Card>
          
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <img className="rounded-t-lg" src="https://powderlak.co.za/img/istockphoto-1299308121-1024x1024.jpg" alt="Commercial" />
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-semibold mb-4">Commercial</h3>
              <p className="text-gray-600">
                Versatile solutions for architectural and commercial projects with lasting beauty.
              </p>
            </CardContent>
          </Card>
          
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <img className="rounded-t-lg" src="https://powderlak.co.za/img/istockphoto-1134089711-1024x1024.jpg" alt="Decorative" />
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-semibold mb-4">Decorative</h3>
              <p className="text-gray-600">
                Stunning finishes that combine aesthetic appeal with exceptional durability.
              </p>
            </CardContent>
          </Card>
        </div>
        
        <div className="text-center">
          <Link to="/products">
            <Button 
              size="lg"
              className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              View Full Product Range
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
