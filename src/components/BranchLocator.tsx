
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, Phone, Mail, Globe } from "lucide-react";
import { useState } from "react";

const BranchLocator = () => {
  const [selectedBranch, setSelectedBranch] = useState<string | null>(null);

  const branches = [
    {
      id: "johannesburg",
      name: "Johannesburg Head Office",
      type: "Head Office",
      country: "South Africa",
      phones: [
        "+27 (0) 11 437 5905/6",
        "+27 (0) 82 880 8100",
        "+27 (0) 82 880 8101", // WhatsApp
        "+27 (0) 82 880 8102"
      ],
      fax: "+27 (0) 11 437 5907",
      address: [
        "5 Tielman Roos Street",
        "Germiston South Ext.7",
        "Ekurhuleni",
        "Gauteng 1401",
        "R.S.A."
      ],
      whatsapp: "+27 (0) 82 880 8101",
      color: "from-blue-500 to-blue-600"
    },
    {
      id: "capetown",
      name: "Cape Town Branch",
      type: "Branch",
      company: "All-Pro Products",
      country: "South Africa",
      phones: ["+27 (0) 21 511 5205"],
      fax: "+27 (0) 21 511 5259",
      email: "Send us an email",
      website: "www.all-pro.co.za",
      address: [
        "34 Highgate St",
        "Ndabeni",
        "Cape Town 7405"
      ],
      color: "from-green-500 to-green-600"
    },
    {
      id: "portelizabeth",
      name: "Port Elizabeth Branch",
      type: "Branch",
      company: "P.I. Marketing",
      country: "South Africa",
      phones: [
        "+27 (0) 41 367 3197",
        "+27 (0) 82 431 5722",
        "+27 (0) 82 258 5465"
      ],
      fax: "+27 (0) 86 689 7216",
      email: "Send us an email",
      address: [
        "200 Circular Dr",
        "Lorraine",
        "Port Elizabeth 6070"
      ],
      color: "from-purple-500 to-purple-600"
    },
    {
      id: "durban",
      name: "Durban Agent",
      type: "Agent",
      company: "Powder-Lak KZN",
      country: "South Africa",
      phones: [
        "+27 (0) 31 736 1007",
        "+27 (0) 76 033 9678",
        "+27 (0) 82 687 4088"
      ],
      fax: "+27 (0) 86 614 2391",
      email: "Send us an email",
      address: [
        "1 Van Eck Avenue",
        "Hammersdal",
        "Kwa-Zulu Natal",
        "Durban 3608",
        "South Africa"
      ],
      color: "from-orange-500 to-orange-600"
    },
    {
      id: "germany",
      name: "Germany / Ghana / Nigeria Agent",
      type: "International Agent",
      company: "G. Koepcke & Co. GmbH",
      country: "EMEA",
      phones: ["+49 (0)40 23 85 50 – 0"],
      fax: "+49 (0)40 23 85 50 – 99",
      email: "Send us an email",
      address: [
        "Sachsenfeld 2",
        "20097 Hamburg",
        "Germany"
      ],
      color: "from-red-500 to-red-600"
    },
    {
      id: "zimbabwe",
      name: "Zimbabwean Agent",
      type: "Agent",
      country: "Zimbabwe",
      phones: [
        "+263 (0) 77 734 2556",
        "+263 (0) 77 115 9001"
      ],
      email: "Send us an email",
      address: ["Bulawayo"],
      color: "from-teal-500 to-teal-600"
    }
  ];

  const handleCardClick = (branchId: string) => {
    setSelectedBranch(selectedBranch === branchId ? null : branchId);
  };

  return (
    <section className="py-12">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Locations</h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Find your nearest Powder-Lak branch or agent. Click on any location to view detailed contact information.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {branches.map((branch) => (
          <Card 
            key={branch.id}
            className={`cursor-pointer transition-all duration-300 transform hover:scale-105 hover:shadow-xl ${
              selectedBranch === branch.id ? 'ring-2 ring-primary shadow-xl scale-105' : ''
            }`}
            onClick={() => handleCardClick(branch.id)}
          >
            <CardHeader className={`bg-gradient-to-r ${branch.color} text-white rounded-t-lg`}>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="w-5 h-5" />
                {branch.name}
              </CardTitle>
              <div className="flex gap-2">
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-sm">
                  {branch.type}
                </span>
                <span className="bg-white bg-opacity-20 px-2 py-1 rounded text-sm">
                  {branch.country}
                </span>
              </div>
            </CardHeader>
            
            <CardContent className="p-6">
              {branch.company && (
                <p className="font-semibold text-primary mb-3">{branch.company}</p>
              )}
              
              <div className="space-y-3">
                {/* Address */}
                <div className="flex items-start gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground mt-1 flex-shrink-0" />
                  <div className="text-sm">
                    {branch.address.map((line, index) => (
                      <div key={index}>{line}</div>
                    ))}
                  </div>
                </div>

                {/* Phones */}
                <div className="flex items-start gap-2">
                  <Phone className="w-4 h-4 text-muted-foreground mt-1 flex-shrink-0" />
                  <div className="text-sm">
                    {branch.phones.map((phone, index) => (
                      <div key={index} className="flex items-center gap-1">
                        {phone}
                        {branch.whatsapp === phone && (
                          <span className="text-green-600 text-xs">(WhatsApp)</span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Additional Details - shown when selected */}
                {selectedBranch === branch.id && (
                  <div className="mt-4 pt-4 border-t space-y-3 animate-fade-in">
                    {branch.fax && (
                      <div className="text-sm">
                        <span className="font-medium">Fax:</span> {branch.fax}
                      </div>
                    )}
                    
                    {branch.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-muted-foreground" />
                        <Button variant="link" className="p-0 h-auto text-sm">
                          {branch.email}
                        </Button>
                      </div>
                    )}
                    
                    {branch.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="w-4 h-4 text-muted-foreground" />
                        <Button variant="link" className="p-0 h-auto text-sm">
                          {branch.website}
                        </Button>
                      </div>
                    )}

                    {branch.whatsapp && (
                      <Button 
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(`https://wa.me/${branch.whatsapp.replace(/\D/g, '')}`, '_blank');
                        }}
                      >
                        Contact via WhatsApp
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center mt-12">
        <p className="text-muted-foreground">
          Can't find what you're looking for? <Button variant="link" className="p-0">Contact our head office</Button> for assistance.
        </p>
      </div>
    </section>
  );
};

export default BranchLocator;
