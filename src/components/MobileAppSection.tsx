import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Smartphone } from "lucide-react";
import { ImAndroid, ImAppleinc } from "react-icons/im";

const MobileAppSection = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <Card className="max-w-4xl mx-auto bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <CardContent className="p-12 text-center">
            <Smartphone className="w-20 h-20 mx-auto mb-8 text-white" />
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Powder-Lak Custom Products app
            </h2>
            <p className="text-xl mb-8 opacity-90">
              All customers to request custom powder coating products with relating gloss, finish and colour. Will also allow customers to send an image of the requested colour.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 px-8 py-3 text-lg font-semibold"
                onClick={() => window.open("https://play.google.com/store/apps/details?id=za.co.netgen.powderlak&hl=en", "_blank")}
              >
                <ImAndroid className="text-green-600 w-5 h-5 mr-2" />
                Get it on <span className="font-bold">Google Play</span>
              </Button>
              <Button 
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 px-8 py-3 text-lg font-semibold"
                onClick={() => window.open("https://apps.apple.com/us/app/powder-lak-custom-products/id1591371316", "_blank")}
              >
                <ImAppleinc className="text-gray-400 w-5 h-5 mr-2" />
                Download on the <span className="font-bold">App Store</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default MobileAppSection;
