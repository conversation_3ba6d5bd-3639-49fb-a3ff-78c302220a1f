import { MapPin, Mail, Phone } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { ImAndroid, ImAppleinc, ImFacebook, ImLinkedin2 } from "react-icons/im";

const Footer = () => {
  return (
    <footer style={{ backgroundColor: "#09090b" }} className="text-white py-16">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="text-2xl font-bold mb-4">Powder-Lak</h3>
            <p className="text-gray-300 mb-4">
              A World of Colour, Quality and Service.
            </p>

            <div className="flex items-start">
              <a
                className="mb-2 mr-2 p-2 text-xs"
                href="https://play.google.com/store/apps/details?id=za.co.netgen.powderlak&hl=en"
                target="_blank">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <ImAndroid className="text-green-600 w-5 h-5" />
                  </div>
                  <div>
                    Get it on<br />
                    <span className="font-bold">Google Play</span>
                  </div>
                </div>
              </a>

              <a
                className="mb-2 p-2 text-xs"
                href="https://apps.apple.com/us/app/powder-lak-custom-products/id1591371316"
                target="_blank">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <ImAppleinc className="text-gray-400 w-5 h-5" />
                  </div>
                  <div>
                    Download on the<br />
                    <span className="font-bold">App Store</span>
                  </div>
                </div>
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><Link to="/" className="text-gray-300 hover:text-white transition-colors">Home</Link></li>
              <li><Link to="/products" className="text-gray-300 hover:text-white transition-colors">Product Range</Link></li>
              <li><Link to="/services" className="text-gray-300 hover:text-white transition-colors">Services</Link></li>
              <li><Link to="/about" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">&nbsp;</h4>
            <ul className="space-y-2">
              <li><Link to="/sitemap" className="text-gray-300 hover:text-white transition-colors">Sitemap</Link></li>
              <li><Link to="/privacy" className="text-gray-300 hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link to="/enquiries" className="text-gray-300 hover:text-white transition-colors">Enquiries</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 mt-1 text-gray-300" />
                <span className="text-gray-300">📍 Contact Info</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-gray-300" />
                <span className="text-gray-300">📧 Email</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-gray-300" />
                <span className="text-gray-300">☎ +27 (0)11 437 5905/6</span>
              </div>

              <div className="flex items-start mb-2">
                <a href="https://www.linkedin.com/company/powder-lak-pty-ltd-/" target="_blank">
                  <ImLinkedin2 />
                </a>
                <a href="https://www.facebook.com/PowderLak" target="_blank">
                  <ImFacebook />
                </a>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-700 pt-8 text-center">
          <p className="text-gray-300">
            © 2024 Powder-Lak. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
