
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { Palette, Settings } from "lucide-react";

const ServicesSection = () => {
  const services = [
    {
      icon: Palette,
      title: "Colour Matching",
      description: "Precise colour matching services to meet your exact specifications.",
      image: "https://powderlak.co.za/img/istockphoto-1183213819-1024x1024.jpg"
    },
    {
      icon: Settings,
      title: "Custom Batch Production",
      description: "Tailored production runs for unique project requirements.",
      image: "https://powderlak.co.za/img/istockphoto-1421347828-1024x1024.jpg"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Expert Services
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
            From colour matching to custom batch production and training, we offer end-to-end powder coating expertise.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 mb-12">
          {services.map((service, index) => (
            <Card key={index} className="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
              <img className="rounded-t-lg" src="{service.image}" alt="{service.title}" />
              <CardContent className="p-6 text-center">
                <service.icon className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">{service.title}</h3>
                <p className="text-gray-600 text-sm">{service.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/services">
            <Button 
              size="lg"
              style={{ backgroundColor: "#ff7b00" }}
              className="hover:opacity-90 text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              View Services
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
