
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";

const Hero = () => {
  return (
    <section 
      className="relative min-h-[80vh] flex items-center justify-center text-white overflow-hidden"
      style={{
        background: "linear-gradient(135deg, #070dbf 0%, #bcbdd7 100%)"
      }}
    >
      <div className="absolute inset-0 bg-black/20"></div>
      
      <div className="relative z-10 container mx-auto px-4 text-center">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
          A World of Colour,<br />
          Quality and Service.
        </h1>
        
        <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed opacity-90">
          Discover a kaleidoscope of colours and premium finishes for every surface—durable, flawless, and environmentally responsible.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link to="/products">
            <Button 
              size="lg" 
              className="bg-white text-primary hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 px-8 py-3 text-lg font-semibold"
            >
              Explore Products
            </Button>
          </Link>
          <Link to="/enquiries">
            <Button 
              size="lg" 
              variant="outline"
              className="border-white text-primary hover:bg-white hover:text-primary transition-all duration-300 transform hover:scale-105 px-8 py-3 text-lg font-semibold"
            >
              Make an Enquiry
            </Button>
          </Link>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 left-20 w-24 h-24 bg-white/15 rounded-full blur-lg animate-pulse" style={{ animationDelay: "1s" }}></div>
    </section>
  );
};

export default Hero;
