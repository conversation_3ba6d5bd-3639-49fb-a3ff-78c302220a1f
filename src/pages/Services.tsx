
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Palette, Users, Clock, Beaker, Award, Cog, Package } from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: Palette,
      title: "Advanced Colour Matching",
      description: "The latest colour matching techniques using cutting edge technology"
    },
    {
      icon: Award,
      title: "International Standards",
      description: "Our colour standards adhere to the international RAL system and SABS colours"
    },
    {
      icon: Users,
      title: "Technical Expertise",
      description: "Skilled team of professionals to handle all your technical queries, including oven recording and cure analysis"
    },
    {
      icon: CheckCircle,
      title: "Quality Assurance",
      description: "After-the-job inspections and a full range of quality tests to ensure client's satisfaction"
    },
    {
      icon: Beaker,
      title: "Decorative Finishes",
      description: "A range of decorative finishes never seen before in powder coating"
    },
    {
      icon: Cog,
      title: "Training & Consultation",
      description: "Certified training courses for spray operators and advice on guns and powder plants"
    },
    {
      icon: Clock,
      title: "Fast Turnaround",
      description: "Specially matched colours within five working days > Average production turn around time of 3-5 working days"
    },
    {
      icon: Package,
      title: "Flexible Production",
      description: "Small batches of 60kg and once off special runs of 20kg, when available"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-8">Our Services</h1>
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-semibold text-primary mb-6">
              Powder-Lak Powder Coating Services
            </h2>
            <p className="text-xl text-muted-foreground leading-relaxed mb-12">
              At Powder-Lak we are passionate about powder coating and we strive to deliver the highest quality powder coating equipment and products. We offer the following services:
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {services.map((service, index) => (
            <Card key={index} className="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <service.icon className="w-8 h-8 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{service.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">{service.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Services;
