import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import BranchLocator from "@/components/BranchLocator";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Users, Clock, Award, Palette, Cog, Truck } from "lucide-react";

const About = () => {
  const whyChooseUs = [
    {
      icon: Palette,
      title: "Advanced Color Control",
      description: "Colour batch control and matching is done by a sophisticated colour computer and a Delta E (colour variance) from standard, of which 0.8 is the maximum allowed variance. Batch certificates showing all mechanical and colour compliance will be issued on request."
    },
    {
      icon: CheckCircle,
      title: "Small Batch Flexibility",
      description: "Small batch quantities of 60Kg can be manufactured on request for special colours."
    },
    {
      icon: Clock,
      title: "Fast Production",
      description: "Specially matched colours manufactured within three to five working days"
    },
    {
      icon: Award,
      title: "Multiple Finishes",
      description: "We manufacture substantial quantities of powder per annum and all powders can be supplied in Smooth, Textured, Sandpaper texture, Matt Semi-matt, and Gloss finishes."
    },
    {
      icon: Users,
      title: "Expert Technical Support",
      description: "We feature a highly skilled and trained team of problem solvers for all your technical queries, including oven recording, cure analysis and a full range of quality tests."
    },
    {
      icon: Cog,
      title: "Training & Consultation",
      description: "Certified training courses for spray operators and advice on guns and powder plants."
    },
    {
      icon: Truck,
      title: "Reliable Delivery",
      description: "A fast, reliable & flexible delivery service"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-8">About Us</h1>
          <p className="text-xl text-center text-muted-foreground max-w-3xl mx-auto mb-16">
            Learn more about Powder-Lak's commitment to quality, service, and innovation.
          </p>
        </div>

        {/* Company Story Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-8 text-center">About Powder-Lak (Pty) Ltd.</h2>
          
          <div className="prose prose-lg max-w-none text-muted-foreground space-y-6">
            <p className="text-lg leading-relaxed">
              Powder-Lak (Pty) Ltd. is a German Company, which in a few short years became one of the leading manufacturers of Powder Coatings in Africa. The company is based in Germiston South, which borders on Johannesburg, South Africa. Since it's inception South Africa in 1988, the company has been personally operated on a daily basis by the Flinner family, now in the 2nd generation.
            </p>
            
            <p className="text-lg leading-relaxed">
              The Founder, Mr Hans Flinner, came to South Africa and originally started Powder-Lak (Pty) Ltd. in small premises in Roodekop, near Johannesburg in 1988. With his considerable years of German industrial expertise within the coatings industry behind him, plus his considerable knowledge of the latest German Technology and backed by his wide experience of European business methods and requirements allowed him to grow this small business into a power house within a few years. As the Company grew and became more established, it moved to larger premises in 1993 where it still stands today, covering some 15 000 sq.mtrs, 6 000 sq. mtrs of which is solely given to the manufacturing space.
            </p>
            
            <p className="text-lg leading-relaxed">
              The company continues to grow & obtain more market share by supplying the market with a wider range of powder paint colours, greater flexibility in quantities and offering an unbeatable & flexible delivery service coupled with extensive technical support. Powder -Lak is regarded as possessing the fastest delivery service, from date and time of order, to colour matching, production of product and final delivery to our customer.
            </p>
            
            <p className="text-lg leading-relaxed">
              Here at Powder-Lak we never cease our ongoing task of looking into the needs and requirements of all our customers, plus the Company is known to possess the largest Colour Range in South Africa, with some 3000 colours readily available, in RAL, S.A.B.S. and/or N.C.S. Standards. We are also approved partners by RAL for Africa.
            </p>
            
            <p className="text-lg leading-relaxed">
              Thanks to our investments in the field of colour matching technology we are able to match and produce products according to our customer's specifications, be it from a colour standard, competitors product or a simple paint sample.
            </p>
            
            <p className="text-lg leading-relaxed font-medium">
              We are a family run business who will go the extra mile for your business, be it prioritised production, after hours deliveries or even weekend deliveries. We strive to provide our clients with the best possible service and products.
            </p>
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-12 text-center">Why Powder-Lak (Pty) Ltd.?</h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {whyChooseUs.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <feature.icon className="w-8 h-8 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                      <p className="text-muted-foreground leading-relaxed">{feature.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Branch Locator Section */}
        <BranchLocator />
      </div>
      <Footer />
    </div>
  );
};

export default About;
