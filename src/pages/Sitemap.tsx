import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Link } from "react-router-dom";
import { ChevronRight } from "lucide-react";

const Sitemap = () => {
  const sitePages = [
    { name: "Home", path: "/", description: "Main landing page" },
    { name: "Product Range", path: "/products", description: "Browse our powder coating products" },
    { name: "Services", path: "/services", description: "Explore our professional services" },
    { name: "About Us", path: "/about", description: "Learn about Powder-Lak and our history" },
    { name: "Enquiries", path: "/enquiries", description: "Contact us for quotes and information" },
    { name: "Privacy Policy", path: "/privacy", description: "Our privacy policy" },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-20">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-8">Sitemap</h1>
          <p className="text-lg text-muted-foreground mb-12">
            Find all the pages available on our website.
          </p>

          <div className="space-y-6">
            <ul className="space-y-4">
              {sitePages.map((page) => (
                <li key={page.path} className="border-b border-gray-100 pb-4">
                  <Link 
                    to={page.path}
                    className="flex items-center group hover:text-primary transition-colors"
                  >
                    <ChevronRight className="w-5 h-5 mr-2 text-muted-foreground group-hover:text-primary" />
                    <div>
                      <span className="font-medium text-lg">{page.name}</span>
                      <p className="text-sm text-muted-foreground">{page.description}</p>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Sitemap;